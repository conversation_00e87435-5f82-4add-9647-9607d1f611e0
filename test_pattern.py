#!/usr/bin/env python3

import re

# อ่านไฟล์และหาตัวอย่างบรรทัด
with open('ServerCoreSilent.log', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

# หาบรรทัดที่มี PROPERTY CHANGE
lines = content.split('\n')
property_lines = []

for i, line in enumerate(lines):
    if 'PROPERTY CHANGE:' in line:
        property_lines.append(f"Line {i+1}: {line.strip()}")
        if len(property_lines) >= 10:
            break

print("ตัวอย่างบรรทัด PROPERTY CHANGE:")
for line in property_lines:
    print(line)
    print()
