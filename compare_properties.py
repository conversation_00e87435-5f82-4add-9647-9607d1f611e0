#!/usr/bin/env python3
"""
เปรียบเทียบ PROPERTY ระหว่างไฟล์ ServerCore สองไฟล์
โดยใช้ Silent เป็นตัวตั้ง และแจ้งถ้ามี PROPERTY ที่ไม่มีใน Silent แต่มีใน UI
"""

import re
import sys

def extract_properties(file_path):
    """
    แยก PROPERTY ทั้งหมดจากไฟล์ log
    """
    properties = {}

    try:
        with open(file_path, 'rb') as f:
            raw_content = f.read()

        # แปลงเป็น text และลบ null bytes
        content = raw_content.decode('utf-8', errors='ignore').replace('\x00', '')

        print(f"   อ่านไฟล์ {file_path} ได้ {len(content)} ตัวอักษร")

        # หา PROPERTY CHANGE patterns
        # Pattern 1: Adding property
        add_pattern = r"PROPERTY CHANGE: Adding ([A-Za-z0-9_]+) property\. Its value is '([^']*)'\.?"
        add_matches = re.findall(add_pattern, content)
        print(f"   พบ Adding properties: {len(add_matches)}")

        for prop_name, prop_value in add_matches:
            properties[prop_name] = prop_value

        # Pattern 2: Modifying property
        modify_pattern = r"PROPERTY CHANGE: Modifying ([A-Za-z0-9_]+) property\. Its current value is '[^']*'\. Its new value: '([^']*)'\.?"
        modify_matches = re.findall(modify_pattern, content)
        print(f"   พบ Modifying properties: {len(modify_matches)}")

        for prop_name, prop_value in modify_matches:
            properties[prop_name] = prop_value

        # Pattern 3: Deleting property
        delete_pattern = r"PROPERTY CHANGE: Deleting ([A-Za-z0-9_]+) property\."
        delete_matches = re.findall(delete_pattern, content)
        print(f"   พบ Deleting properties: {len(delete_matches)}")

        for prop_name in delete_matches:
            properties[prop_name] = "[DELETED]"
            
    except FileNotFoundError:
        print(f"ไม่พบไฟล์: {file_path}")
        return {}
    except Exception as e:
        print(f"เกิดข้อผิดพลาดในการอ่านไฟล์ {file_path}: {e}")
        return {}
        
    return properties

def compare_properties(silent_props, ui_props):
    """
    เปรียบเทียบ properties ระหว่าง silent และ ui
    """
    print("=" * 80)
    print("การเปรียบเทียบ PROPERTY ระหว่าง ServerCore Silent และ UI")
    print("=" * 80)
    print()
    
    # Properties ที่มีใน Silent
    silent_only = set(silent_props.keys()) - set(ui_props.keys())
    
    # Properties ที่มีใน UI แต่ไม่มีใน Silent
    ui_only = set(ui_props.keys()) - set(silent_props.keys())
    
    # Properties ที่มีในทั้งสองแต่ค่าต่างกัน
    different_values = {}
    common_props = set(silent_props.keys()) & set(ui_props.keys())
    
    for prop in common_props:
        if silent_props[prop] != ui_props[prop]:
            different_values[prop] = {
                'silent': silent_props[prop],
                'ui': ui_props[prop]
            }
    
    # แสดงผลลัพธ์
    print(f"📊 สรุปผลการเปรียบเทียบ:")
    print(f"   - Properties ใน Silent: {len(silent_props)}")
    print(f"   - Properties ใน UI: {len(ui_props)}")
    print(f"   - Properties เหมือนกัน: {len(common_props) - len(different_values)}")
    print(f"   - Properties ค่าต่างกัน: {len(different_values)}")
    print(f"   - Properties มีเฉพาะใน Silent: {len(silent_only)}")
    print(f"   - Properties มีเฉพาะใน UI: {len(ui_only)}")
    print()
    
    # 1. Properties ที่มีเฉพาะใน Silent
    if silent_only:
        print("🔵 Properties ที่มีเฉพาะใน Silent:")
        print("-" * 50)
        for prop in sorted(silent_only):
            print(f"   {prop} = '{silent_props[prop]}'")
        print()
    
    # 2. Properties ที่มีเฉพาะใน UI (ตามที่ผู้ใช้ต้องการ)
    if ui_only:
        print("🔴 Properties ที่ไม่มีใน Silent แต่มีใน UI:")
        print("-" * 50)
        for prop in sorted(ui_only):
            print(f"   {prop} = '{ui_props[prop]}'")
        print()
    else:
        print("✅ ไม่มี Properties ที่มีเฉพาะใน UI")
        print()
    
    # 3. Properties ที่มีค่าต่างกัน
    if different_values:
        print("🟡 Properties ที่มีค่าต่างกัน:")
        print("-" * 50)
        for prop in sorted(different_values.keys()):
            print(f"   {prop}:")
            print(f"      Silent: '{different_values[prop]['silent']}'")
            print(f"      UI:     '{different_values[prop]['ui']}'")
            print()
    
    return {
        'silent_only': silent_only,
        'ui_only': ui_only,
        'different_values': different_values,
        'total_silent': len(silent_props),
        'total_ui': len(ui_props)
    }

def main():
    """
    ฟังก์ชันหลัก
    """
    silent_file = "ServerCoreSilent.log"
    ui_file = "ServerCoreUI.log"
    
    print("🔍 กำลังแยก Properties จากไฟล์...")
    
    # แยก properties จากทั้งสองไฟล์
    silent_props = extract_properties(silent_file)
    ui_props = extract_properties(ui_file)
    
    if not silent_props and not ui_props:
        print("❌ ไม่พบ Properties ในไฟล์ใดๆ")
        return
    
    # เปรียบเทียบ properties
    results = compare_properties(silent_props, ui_props)
    
    # บันทึกผลลัพธ์ลงไฟล์
    with open("property_comparison_result.txt", "w", encoding="utf-8") as f:
        f.write("การเปรียบเทียบ PROPERTY ระหว่าง ServerCore Silent และ UI\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"สรุปผลการเปรียบเทียบ:\n")
        f.write(f"- Properties ใน Silent: {results['total_silent']}\n")
        f.write(f"- Properties ใน UI: {results['total_ui']}\n")
        f.write(f"- Properties มีเฉพาะใน Silent: {len(results['silent_only'])}\n")
        f.write(f"- Properties มีเฉพาะใน UI: {len(results['ui_only'])}\n")
        f.write(f"- Properties ค่าต่างกัน: {len(results['different_values'])}\n\n")
        
        if results['ui_only']:
            f.write("Properties ที่ไม่มีใน Silent แต่มีใน UI:\n")
            f.write("-" * 50 + "\n")
            for prop in sorted(results['ui_only']):
                f.write(f"{prop} = '{ui_props[prop]}'\n")
            f.write("\n")
        
        if results['different_values']:
            f.write("Properties ที่มีค่าต่างกัน:\n")
            f.write("-" * 50 + "\n")
            for prop in sorted(results['different_values'].keys()):
                f.write(f"{prop}:\n")
                f.write(f"  Silent: '{results['different_values'][prop]['silent']}'\n")
                f.write(f"  UI:     '{results['different_values'][prop]['ui']}'\n\n")
    
    print("📄 ผลลัพธ์ถูกบันทึกในไฟล์: property_comparison_result.txt")

if __name__ == "__main__":
    main()
