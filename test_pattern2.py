#!/usr/bin/env python3

import re

# อ่านไฟล์และทดสอบ pattern
with open('ServerCoreSilent.log', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

# Test pattern 1: Adding
add_pattern = r"PROPERTY CHANGE: Adding ([A-Za-z0-9_]+) property\. Its value is '([^']*)'\.?"
add_matches = re.findall(add_pattern, content)
print(f"Adding pattern found: {len(add_matches)} matches")

if add_matches:
    for i, (name, value) in enumerate(add_matches[:5]):
        print(f"  {i+1}. {name} = '{value}'")

# Test pattern 2: Modifying
modify_pattern = r"PROPERTY CHANGE: Modifying ([A-Za-z0-9_]+) property\. Its current value is '[^']*'\. Its new value: '([^']*)'\.?"
modify_matches = re.findall(modify_pattern, content)
print(f"\nModifying pattern found: {len(modify_matches)} matches")

if modify_matches:
    for i, (name, value) in enumerate(modify_matches[:5]):
        print(f"  {i+1}. {name} = '{value}'")

# Test pattern 3: Deleting
delete_pattern = r"PROPERTY CHANGE: Deleting ([A-Za-z0-9_]+) property\."
delete_matches = re.findall(delete_pattern, content)
print(f"\nDeleting pattern found: {len(delete_matches)} matches")

if delete_matches:
    for i, name in enumerate(delete_matches[:5]):
        print(f"  {i+1}. {name} = [DELETED]")

print(f"\nTotal properties found: {len(add_matches) + len(modify_matches) + len(delete_matches)}")
